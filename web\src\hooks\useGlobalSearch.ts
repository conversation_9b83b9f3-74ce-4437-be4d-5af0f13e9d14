import { useContext } from "react";
import { useQuery } from "react-query";
import { store } from "../store";
import { LogMessage, UserLogResponse } from "../types/log";
import { parseEmotes } from "../services/parseEmotes";

interface GlobalSearchParams {
  q?: string;
  regex?: boolean;
  caseSensitive?: boolean;
  channels?: string[];
  excludeChannels?: string[];
  users?: string[];
  excludeUsers?: string[];
  messageTypes?: string[];
  badges?: string[];
  from?: Date | null;
  to?: Date | null;
  minLength?: number;
  maxLength?: number;
  firstMessagesOnly?: boolean;
  subscribersOnly?: boolean;
  vipsOnly?: boolean;
  moderatorsOnly?: boolean;
  hasEmotes?: boolean;
  searchDisplayNames?: boolean;
}

export function useGlobalSearch(
  searchParams: GlobalSearchParams,
  enabled: boolean = false
): { data: LogMessage[] | undefined; isLoading: boolean; error: any } {
  const { state } = useContext(store);

  const { data, isLoading, error } = useQuery<LogMessage[]>(
    ["global-search", { searchParams }],
    async () => {
      if (!searchParams.q) {
        return [];
      }

      // Use advanced search endpoint for global search
      const baseUrl = `${state.apiBaseUrl}/advanced-search`;
      const queryUrl = new URL(baseUrl);

      // Add search parameters
      if (searchParams.q) {
        queryUrl.searchParams.append("q", searchParams.q);
      }

      if (searchParams.regex) {
        queryUrl.searchParams.append("regex", "1");
      }

      if (searchParams.caseSensitive) {
        queryUrl.searchParams.append("caseSensitive", "1");
      }

      if (searchParams.channels && searchParams.channels.length > 0) {
        queryUrl.searchParams.append(
          "channels",
          searchParams.channels.join(",")
        );
      }

      if (
        searchParams.excludeChannels &&
        searchParams.excludeChannels.length > 0
      ) {
        queryUrl.searchParams.append(
          "excludeChannels",
          searchParams.excludeChannels.join(",")
        );
      }

      if (searchParams.users && searchParams.users.length > 0) {
        queryUrl.searchParams.append("users", searchParams.users.join(","));
      }

      if (searchParams.excludeUsers && searchParams.excludeUsers.length > 0) {
        queryUrl.searchParams.append(
          "excludeUsers",
          searchParams.excludeUsers.join(",")
        );
      }

      if (searchParams.messageTypes && searchParams.messageTypes.length > 0) {
        queryUrl.searchParams.append(
          "messageTypes",
          searchParams.messageTypes.join(",")
        );
      }

      if (searchParams.badges && searchParams.badges.length > 0) {
        queryUrl.searchParams.append("badges", searchParams.badges.join(","));
      }

      if (searchParams.from) {
        queryUrl.searchParams.append("from", searchParams.from.toISOString());
      }

      if (searchParams.to) {
        queryUrl.searchParams.append("to", searchParams.to.toISOString());
      }

      if (searchParams.minLength !== undefined) {
        queryUrl.searchParams.append(
          "minLength",
          searchParams.minLength.toString()
        );
      }

      if (searchParams.maxLength !== undefined) {
        queryUrl.searchParams.append(
          "maxLength",
          searchParams.maxLength.toString()
        );
      }

      if (searchParams.firstMessagesOnly) {
        queryUrl.searchParams.append("firstMessagesOnly", "1");
      }

      if (searchParams.subscribersOnly) {
        queryUrl.searchParams.append("subscribersOnly", "1");
      }

      if (searchParams.vipsOnly) {
        queryUrl.searchParams.append("vipsOnly", "1");
      }

      if (searchParams.moderatorsOnly) {
        queryUrl.searchParams.append("moderatorsOnly", "1");
      }

      if (searchParams.hasEmotes) {
        queryUrl.searchParams.append("hasEmotes", "1");
      }

      if (searchParams.searchDisplayNames) {
        queryUrl.searchParams.append("searchDisplayNames", "1");
      }

      // Add response format
      queryUrl.searchParams.append("jsonBasic", "1");

      if (!state.settings.newOnBottom.value) {
        queryUrl.searchParams.append("reverse", "1");
      }

      const response = await fetch(queryUrl.toString());

      if (!response.ok) {
        throw new Error(response.statusText);
      }

      const data: UserLogResponse = await response.json();
      const messages: LogMessage[] = [];

      for (const msg of data.messages) {
        messages.push({
          ...msg,
          timestamp: new Date(msg.timestamp),
          emotes: parseEmotes(msg.text, msg.tags["emotes"]),
        });
      }

      return messages;
    },
    {
      enabled,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  return { data, isLoading, error };
}

// Simple global search hook for basic text search
export function useSimpleGlobalSearch(
  query: string,
  enabled: boolean = false
): { data: LogMessage[] | undefined; isLoading: boolean; error: any } {
  const { state } = useContext(store);

  const { data, isLoading, error } = useQuery<LogMessage[]>(
    ["simple-global-search", { query }],
    async () => {
      if (!query) {
        return [];
      }

      const baseUrl = `${state.apiBaseUrl}/search`;
      const queryUrl = new URL(baseUrl);

      queryUrl.searchParams.append("q", query);
      queryUrl.searchParams.append("jsonBasic", "1");

      if (!state.settings.newOnBottom.value) {
        queryUrl.searchParams.append("reverse", "1");
      }

      const response = await fetch(queryUrl.toString());

      if (!response.ok) {
        throw new Error(response.statusText);
      }

      const data: UserLogResponse = await response.json();
      const messages: LogMessage[] = [];

      for (const msg of data.messages) {
        messages.push({
          ...msg,
          timestamp: new Date(msg.timestamp),
          emotes: parseEmotes(msg.text, msg.tags["emotes"]),
        });
      }

      return messages;
    },
    {
      enabled,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  return { data, isLoading, error };
}
