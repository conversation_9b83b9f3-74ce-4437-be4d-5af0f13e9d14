import React, { useState, useContext } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Divider,
} from "@mui/material";
import {
  Person,
  Message,
  Schedule,
  TrendingUp,
  Group,
  Star,
  VolumeUp,
} from "@mui/icons-material";
import { useQuery } from "react-query";
import { store } from "../store";
import dayjs from "dayjs";
import { WordCloudVisualization } from "./WordCloudVisualization";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";
import { Line, Bar, Doughnut } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface UserDetailsModalProps {
  open: boolean;
  onClose: () => void;
  user: {
    userId: string;
    username: string;
    messageCount: number;
    channelsActive: number;
    avgMessageLength: number;
  } | null;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`user-tabpanel-${index}`}
      aria-labelledby={`user-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export function UserDetailsModal({
  open,
  onClose,
  user,
}: UserDetailsModalProps) {
  const { state, setCurrents, setShowAnalytics, setShowUserTracker } =
    useContext(store);
  const [tabValue, setTabValue] = useState(0);

  // Fetch user analytics data
  const {
    data: userAnalytics,
    isLoading,
    error,
  } = useQuery(
    ["user-analytics", user?.userId],
    async () => {
      if (!user?.userId) return null;

      const params = new URLSearchParams({
        users: user.userId,
        from: dayjs().subtract(30, "day").toISOString(),
        to: dayjs().toISOString(),
        limit: "10",
      });

      const response = await fetch(`${state.apiBaseUrl}/analytics?${params}`);
      if (!response.ok) throw new Error("Failed to fetch user analytics");
      return response.json();
    },
    { enabled: !!user?.userId && !!state.apiBaseUrl }
  );

  // Fetch user channel breakdown data
  const {
    data: channelBreakdown,
    isLoading: channelBreakdownLoading,
    error: channelBreakdownError,
  } = useQuery(
    ["user-channel-breakdown", user?.userId],
    async () => {
      if (!user?.userId) return null;

      const params = new URLSearchParams({
        from: dayjs().subtract(30, "day").toISOString(),
        to: dayjs().toISOString(),
      });

      // Use a dummy channel for the API path (we only need the user data)
      const response = await fetch(
        `${state.apiBaseUrl}/id/dummy/id/${user.userId}/channel-breakdown?${params}`
      );
      if (!response.ok) throw new Error("Failed to fetch channel breakdown");
      return response.json();
    },
    { enabled: !!user?.userId && !!state.apiBaseUrl }
  );

  // Fetch user word frequency data
  const {
    data: wordFrequency,
    isLoading: wordFrequencyLoading,
    error: wordFrequencyError,
  } = useQuery(
    ["user-word-frequency", user?.userId],
    async () => {
      if (!user?.userId) return null;

      const params = new URLSearchParams({
        from: dayjs().subtract(30, "day").toISOString(),
        to: dayjs().toISOString(),
        limit: "50",
      });

      // Use a dummy channel for the API path (we only need the user data)
      const response = await fetch(
        `${state.apiBaseUrl}/id/dummy/id/${user.userId}/word-frequency?${params}`
      );
      if (!response.ok) throw new Error("Failed to fetch word frequency");
      return response.json();
    },
    { enabled: !!user?.userId && !!state.apiBaseUrl }
  );

  // Fetch user's recent messages across channels
  const { data: recentMessages, isLoading: messagesLoading } = useQuery(
    ["user-recent-messages", user?.userId],
    async () => {
      if (!user?.userId) return null;

      const params = new URLSearchParams({
        q: "",
        users: user.username,
        limit: "20",
        jsonBasic: "1",
      });

      const response = await fetch(
        `${state.apiBaseUrl}/advanced-search?${params}`
      );
      if (!response.ok) throw new Error("Failed to fetch recent messages");
      return response.json();
    },
    { enabled: !!user?.userId && !!state.apiBaseUrl }
  );

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (!user) return null;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <Avatar>
            <Person />
          </Avatar>
          <Box>
            <Typography variant="h5">{user.username}</Typography>
            <Typography variant="body2" color="text.secondary">
              User ID: {user.userId}
            </Typography>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Key Metrics */}
        <Grid container spacing={2} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <Message color="primary" />
                  <Typography variant="h6">Messages</Typography>
                </Box>
                <Typography variant="h4" color="primary">
                  {formatNumber(user.messageCount)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <Group color="primary" />
                  <Typography variant="h6">Channels</Typography>
                </Box>
                <Typography variant="h4" color="primary">
                  {user.channelsActive}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <VolumeUp color="primary" />
                  <Typography variant="h6">Avg Length</Typography>
                </Box>
                <Typography variant="h4" color="primary">
                  {user.avgMessageLength.toFixed(1)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <TrendingUp color="primary" />
                  <Typography variant="h6">Activity</Typography>
                </Box>
                <Typography variant="h4" color="primary">
                  {(user.messageCount / user.channelsActive).toFixed(0)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  msgs/channel
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Recent Activity" />
            <Tab label="Channel Breakdown" />
            <Tab label="Word Cloud" />
            <Tab label="Activity Charts" />
            <Tab label="Message History" />
          </Tabs>
        </Box>

        {/* Recent Activity Tab */}
        <TabPanel value={tabValue} index={0}>
          {isLoading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error">
              Failed to load user analytics: {(error as Error).message}
            </Alert>
          ) : (
            <Box>
              <Typography variant="h6" gutterBottom>
                Activity Summary (Last 30 Days)
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                Detailed analytics for this user's activity across all channels.
              </Typography>
              {/* Add more detailed analytics here when backend is ready */}
            </Box>
          )}
        </TabPanel>

        {/* Channel Breakdown Tab */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Channel Activity Breakdown
          </Typography>

          {channelBreakdownLoading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : channelBreakdownError ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              Failed to load channel breakdown data
            </Alert>
          ) : channelBreakdown ? (
            <>
              <Typography variant="body2" color="text.secondary" paragraph>
                This user is active in {channelBreakdown.totalChannels} channels
                with {formatNumber(channelBreakdown.totalMessages)} total
                messages.
              </Typography>

              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Channel</TableCell>
                      <TableCell align="right">Messages</TableCell>
                      <TableCell align="right">% of Total</TableCell>
                      <TableCell align="right">Avg Length</TableCell>
                      <TableCell align="center">Badges</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {channelBreakdown.channelActivities.map((activity: any) => (
                      <TableRow key={activity.channelId}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {activity.channelName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {dayjs(activity.firstSeen).format("MMM D")} -{" "}
                            {dayjs(activity.lastSeen).format("MMM D")}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          {formatNumber(activity.messageCount)}
                        </TableCell>
                        <TableCell align="right">
                          {activity.percentageOfTotal.toFixed(1)}%
                        </TableCell>
                        <TableCell align="right">
                          {activity.avgMessageLength.toFixed(0)} chars
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" gap={0.5} justifyContent="center">
                            {activity.isSubscriber && (
                              <Chip label="Sub" size="small" color="primary" />
                            )}
                            {activity.isVip && (
                              <Chip
                                label="VIP"
                                size="small"
                                color="secondary"
                              />
                            )}
                            {activity.isModerator && (
                              <Chip label="Mod" size="small" color="success" />
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No channel data available for this user.
            </Typography>
          )}
        </TabPanel>

        {/* Word Cloud Tab */}
        <TabPanel value={tabValue} index={2}>
          <WordCloudVisualization
            data={
              wordFrequency?.wordFrequencies?.map((word: any) => ({
                text: word.word,
                value: word.count,
              })) || []
            }
            isLoading={wordFrequencyLoading}
            error={
              wordFrequencyError
                ? "Failed to load word frequency data"
                : undefined
            }
            title={`Most Used Words by ${user.username}`}
          />
          {wordFrequency && (
            <Box mt={2}>
              <Typography variant="body2" color="text.secondary">
                Total words: {formatNumber(wordFrequency.totalWords)} | Unique
                words: {formatNumber(wordFrequency.uniqueWords)} | Time period:{" "}
                {dayjs(wordFrequency.dateRange.start).format("MMM D")} -{" "}
                {dayjs(wordFrequency.dateRange.end).format("MMM D")}
              </Typography>
            </Box>
          )}
        </TabPanel>

        {/* Activity Charts Tab */}
        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Activity Patterns & Analytics
          </Typography>

          <Grid container spacing={3}>
            {/* Channel Distribution Chart */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Channel Distribution
                  </Typography>
                  {channelBreakdown?.channelActivities &&
                  channelBreakdown.channelActivities.length > 0 ? (
                    <Box sx={{ height: 300 }}>
                      <Doughnut
                        data={{
                          labels: channelBreakdown.channelActivities.map(
                            (activity: any) => activity.channelName
                          ),
                          datasets: [
                            {
                              data: channelBreakdown.channelActivities.map(
                                (activity: any) => activity.messageCount
                              ),
                              backgroundColor: [
                                "#FF6384",
                                "#36A2EB",
                                "#FFCE56",
                                "#4BC0C0",
                                "#9966FF",
                                "#FF9F40",
                                "#FF6384",
                                "#C9CBCF",
                              ],
                              borderWidth: 2,
                            },
                          ],
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: "bottom",
                            },
                            tooltip: {
                              callbacks: {
                                label: function (context) {
                                  const total = channelBreakdown.totalMessages;
                                  const percentage = (
                                    (context.parsed / total) *
                                    100
                                  ).toFixed(1);
                                  return `${context.label}: ${context.parsed} messages (${percentage}%)`;
                                },
                              },
                            },
                          },
                        }}
                      />
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No channel data available
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Message Length Distribution */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Average Message Length by Channel
                  </Typography>
                  {channelBreakdown?.channelActivities &&
                  channelBreakdown.channelActivities.length > 0 ? (
                    <Box sx={{ height: 300 }}>
                      <Bar
                        data={{
                          labels: channelBreakdown.channelActivities.map(
                            (activity: any) => activity.channelName
                          ),
                          datasets: [
                            {
                              label: "Avg Message Length (chars)",
                              data: channelBreakdown.channelActivities.map(
                                (activity: any) =>
                                  Math.round(activity.avgMessageLength)
                              ),
                              backgroundColor: "rgba(54, 162, 235, 0.6)",
                              borderColor: "rgba(54, 162, 235, 1)",
                              borderWidth: 1,
                            },
                          ],
                        }}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false,
                            },
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              title: {
                                display: true,
                                text: "Characters",
                              },
                            },
                            x: {
                              title: {
                                display: true,
                                text: "Channels",
                              },
                            },
                          },
                        }}
                      />
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No channel data available
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* User Badges Summary */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    User Status Across Channels
                  </Typography>
                  {channelBreakdown?.channelActivities &&
                  channelBreakdown.channelActivities.length > 0 ? (
                    <Box>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <Box textAlign="center" p={2}>
                            <Typography variant="h4" color="primary">
                              {
                                channelBreakdown.channelActivities.filter(
                                  (activity: any) => activity.isSubscriber
                                ).length
                              }
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Channels as Subscriber
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <Box textAlign="center" p={2}>
                            <Typography variant="h4" color="secondary">
                              {
                                channelBreakdown.channelActivities.filter(
                                  (activity: any) => activity.isVip
                                ).length
                              }
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Channels as VIP
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <Box textAlign="center" p={2}>
                            <Typography variant="h4" color="success.main">
                              {
                                channelBreakdown.channelActivities.filter(
                                  (activity: any) => activity.isModerator
                                ).length
                              }
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Channels as Moderator
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No channel data available
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Message History Tab */}
        <TabPanel value={tabValue} index={4}>
          <Typography variant="h6" gutterBottom>
            Recent Messages
          </Typography>
          {messagesLoading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : recentMessages?.messages ? (
            <List>
              {recentMessages.messages
                .slice(0, 10)
                .map((message: any, index: number) => (
                  <React.Fragment key={index}>
                    <ListItem alignItems="flex-start">
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Chip
                              label={message.channel || "General"}
                              size="small"
                              color={message.channel ? "default" : "secondary"}
                              variant={message.channel ? "filled" : "outlined"}
                            />
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              {dayjs(message.timestamp).format(
                                "MMM D, YYYY HH:mm"
                              )}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            {message.text}
                          </Typography>
                        }
                      />
                    </ListItem>
                    {index < recentMessages.messages.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No recent messages found.
            </Typography>
          )}
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
        <Button
          variant="contained"
          onClick={() => {
            // Close the modal first
            onClose();

            // Navigate to global search by clearing all states
            setShowAnalytics(false);
            setShowUserTracker(false);
            setCurrents(null, null);

            // Set a URL parameter to trigger search with username
            const url = new URL(window.location.href);
            url.searchParams.delete("analytics");
            url.searchParams.delete("tracker");
            url.searchParams.delete("channel");
            url.searchParams.delete("username");
            url.searchParams.set("search", user.username);
            window.history.replaceState({}, "justlog", url.toString());

            // Trigger a page reload to ensure global search loads with the search parameter
            window.location.reload();
          }}
        >
          View All Messages
        </Button>
      </DialogActions>
    </Dialog>
  );
}
