import React from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  RadialLinearScale,
} from "chart.js";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>hnut, Radar, Scatter } from "react-chartjs-2";
import { Box, Paper, Typography, Grid } from "@mui/material";
import "chartjs-adapter-date-fns";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  RadialLinearScale
);

interface ActivityHeatmapProps {
  data: Array<{
    hour: number;
    messageCount: number;
    uniqueUsers: number;
  }>;
}

export function ActivityHeatmapChart({ data }: ActivityHeatmapProps) {
  const chartData = {
    labels: data.map((d) => `${d.hour}:00`),
    datasets: [
      {
        label: "Messages",
        data: data.map((d) => d.messageCount),
        backgroundColor: "rgba(25, 118, 210, 0.6)",
        borderColor: "rgba(25, 118, 210, 1)",
        borderWidth: 1,
        yAxisID: "y",
      },
      {
        label: "Unique Users",
        data: data.map((d) => d.uniqueUsers),
        backgroundColor: "rgba(76, 175, 80, 0.6)",
        borderColor: "rgba(76, 175, 80, 1)",
        borderWidth: 1,
        yAxisID: "y1",
      },
    ],
  };

  const options = {
    responsive: true,
    interaction: {
      mode: "index" as const,
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: "Hourly Activity Pattern",
      },
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Hour of Day",
        },
      },
      y: {
        type: "linear" as const,
        display: true,
        position: "left" as const,
        title: {
          display: true,
          text: "Messages",
        },
      },
      y1: {
        type: "linear" as const,
        display: true,
        position: "right" as const,
        title: {
          display: true,
          text: "Unique Users",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 2, height: 400 }}>
      <Bar data={chartData} options={options} />
    </Paper>
  );
}

interface MessageTypeDistributionProps {
  data: Array<{
    messageType: string;
    count: number;
    percentage: number;
  }>;
}

export function MessageTypeDistributionChart({
  data,
}: MessageTypeDistributionProps) {
  const chartData = {
    labels: data.map(
      (d) => d.messageType.charAt(0).toUpperCase() + d.messageType.slice(1)
    ),
    datasets: [
      {
        data: data.map((d) => d.count),
        backgroundColor: [
          "rgba(25, 118, 210, 0.8)",
          "rgba(76, 175, 80, 0.8)",
          "rgba(255, 152, 0, 0.8)",
          "rgba(244, 67, 54, 0.8)",
          "rgba(156, 39, 176, 0.8)",
          "rgba(96, 125, 139, 0.8)",
        ],
        borderColor: [
          "rgba(25, 118, 210, 1)",
          "rgba(76, 175, 80, 1)",
          "rgba(255, 152, 0, 1)",
          "rgba(244, 67, 54, 1)",
          "rgba(156, 39, 176, 1)",
          "rgba(96, 125, 139, 1)",
        ],
        borderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: "Message Type Distribution",
      },
      legend: {
        position: "bottom" as const,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const item = data[context.dataIndex];
            return `${context.label}: ${
              context.formattedValue
            } (${item.percentage.toFixed(1)}%)`;
          },
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 2, height: 400 }}>
      <Doughnut data={chartData} options={options} />
    </Paper>
  );
}

interface ActivityTimelineProps {
  data: Array<{
    timestamp: string;
    messageCount: number;
    uniqueUsers: number;
  }>;
  granularity: string;
}

export function ActivityTimelineChart({
  data,
  granularity,
}: ActivityTimelineProps) {
  const chartData = {
    labels: data.map((d) => new Date(d.timestamp)),
    datasets: [
      {
        label: "Messages",
        data: data.map((d) => d.messageCount),
        borderColor: "rgba(25, 118, 210, 1)",
        backgroundColor: "rgba(25, 118, 210, 0.1)",
        fill: true,
        tension: 0.4,
        yAxisID: "y",
      },
      {
        label: "Unique Users",
        data: data.map((d) => d.uniqueUsers),
        borderColor: "rgba(76, 175, 80, 1)",
        backgroundColor: "rgba(76, 175, 80, 0.1)",
        fill: true,
        tension: 0.4,
        yAxisID: "y1",
      },
    ],
  };

  const options = {
    responsive: true,
    interaction: {
      mode: "index" as const,
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: `Activity Timeline (${granularity})`,
      },
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      x: {
        type: "time" as const,
        display: true,
        title: {
          display: true,
          text: "Time",
        },
        time: {
          unit:
            granularity === "hour"
              ? "hour"
              : granularity === "day"
              ? "day"
              : "week",
        },
      },
      y: {
        type: "linear" as const,
        display: true,
        position: "left" as const,
        title: {
          display: true,
          text: "Messages",
        },
      },
      y1: {
        type: "linear" as const,
        display: true,
        position: "right" as const,
        title: {
          display: true,
          text: "Unique Users",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 2, height: 400 }}>
      <Line data={chartData} options={options} />
    </Paper>
  );
}

interface TopChannelsChartProps {
  data: Array<{
    channelName: string;
    messageCount: number;
    uniqueUsers: number;
  }>;
}

export function TopChannelsChart({ data }: TopChannelsChartProps) {
  const chartData = {
    labels: data.slice(0, 10).map((d) => d.channelName),
    datasets: [
      {
        label: "Messages",
        data: data.slice(0, 10).map((d) => d.messageCount),
        backgroundColor: "rgba(25, 118, 210, 0.6)",
        borderColor: "rgba(25, 118, 210, 1)",
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: "Top 10 Channels by Message Count",
      },
      legend: {
        display: false,
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Channel",
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Message Count",
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 2, height: 400 }}>
      <Bar data={chartData} options={options} />
    </Paper>
  );
}

interface InfluenceNetworkProps {
  data: Array<{
    username: string;
    influenceScore: number;
    reach: number;
    engagementRate: number;
  }>;
}

export function InfluenceNetworkChart({ data }: InfluenceNetworkProps) {
  const chartData = {
    datasets: [
      {
        label: "User Influence",
        data: data.slice(0, 20).map((d) => ({
          x: d.reach,
          y: d.influenceScore,
          r: Math.max(5, d.engagementRate * 100 + 5), // Bubble size based on engagement
        })),
        backgroundColor: "rgba(25, 118, 210, 0.6)",
        borderColor: "rgba(25, 118, 210, 1)",
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: "User Influence Network (Bubble size = Engagement Rate)",
      },
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const user = data[context.dataIndex];
            return [
              `User: ${user.username}`,
              `Influence: ${user.influenceScore.toFixed(2)}`,
              `Reach: ${user.reach}`,
              `Engagement: ${(user.engagementRate * 100).toFixed(1)}%`,
            ];
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Reach",
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Influence Score",
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 2, height: 400 }}>
      <Typography variant="h6" gutterBottom>
        Social Network Analysis
      </Typography>
      {/* Note: Chart.js doesn't have a built-in bubble chart, so we'd use a scatter plot */}
      {/* For a proper network visualization, we'd need a library like D3.js or vis.js */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: 300,
          backgroundColor: "rgba(0,0,0,0.05)",
          borderRadius: 1,
        }}
      >
        <Typography variant="body1" color="text.secondary">
          Network visualization would be implemented here using D3.js or similar
          library
        </Typography>
      </Box>
    </Paper>
  );
}

// New chart components for enhanced visualizations

interface MessageLengthDistributionProps {
  data: Array<{
    lengthRange: string;
    count: number;
    percentage: number;
  }>;
}

export function MessageLengthDistributionChart({
  data,
}: MessageLengthDistributionProps) {
  const chartData = {
    labels: data.map((d) => d.lengthRange),
    datasets: [
      {
        label: "Message Count",
        data: data.map((d) => d.count),
        backgroundColor: "rgba(76, 175, 80, 0.6)",
        borderColor: "rgba(76, 175, 80, 1)",
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: "Message Length Distribution",
      },
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const item = data[context.dataIndex];
            return `${context.label}: ${
              context.formattedValue
            } messages (${item.percentage.toFixed(1)}%)`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Message Length (characters)",
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Number of Messages",
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 2, height: 400 }}>
      <Bar data={chartData} options={options} />
    </Paper>
  );
}

interface UserActivityPatternProps {
  data: Array<{
    username: string;
    hourlyActivity: number[];
    totalMessages: number;
  }>;
}

export function UserActivityPatternChart({ data }: UserActivityPatternProps) {
  const topUsers = data.slice(0, 5); // Show top 5 users

  const chartData = {
    labels: Array.from({ length: 24 }, (_, i) => `${i}:00`),
    datasets: topUsers.map((user, index) => ({
      label: user.username,
      data: user.hourlyActivity,
      borderColor: [
        "rgba(25, 118, 210, 1)",
        "rgba(76, 175, 80, 1)",
        "rgba(255, 152, 0, 1)",
        "rgba(244, 67, 54, 1)",
        "rgba(156, 39, 176, 1)",
      ][index],
      backgroundColor: [
        "rgba(25, 118, 210, 0.1)",
        "rgba(76, 175, 80, 0.1)",
        "rgba(255, 152, 0, 0.1)",
        "rgba(244, 67, 54, 0.1)",
        "rgba(156, 39, 176, 0.1)",
      ][index],
      fill: true,
      tension: 0.4,
    })),
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: "Top Users Activity Patterns (24h)",
      },
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Hour of Day",
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Message Count",
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 2, height: 400 }}>
      <Line data={chartData} options={options} />
    </Paper>
  );
}

interface ChannelComparisonProps {
  data: Array<{
    channelName: string;
    messageCount: number;
    uniqueUsers: number;
    avgMessageLength: number;
    peakHour: number;
  }>;
}

export function ChannelComparisonChart({ data }: ChannelComparisonProps) {
  const topChannels = data.slice(0, 6); // Show top 6 channels for radar chart

  const chartData = {
    labels: [
      "Messages",
      "Unique Users",
      "Avg Msg Length",
      "Peak Activity",
      "Engagement",
      "Activity Score",
    ],
    datasets: topChannels.map((channel, index) => {
      // Normalize values for radar chart (0-100 scale)
      const maxMessages = Math.max(...data.map((d) => d.messageCount));
      const maxUsers = Math.max(...data.map((d) => d.uniqueUsers));
      const maxLength = Math.max(...data.map((d) => d.avgMessageLength));

      return {
        label: channel.channelName,
        data: [
          (channel.messageCount / maxMessages) * 100,
          (channel.uniqueUsers / maxUsers) * 100,
          (channel.avgMessageLength / maxLength) * 100,
          (channel.peakHour / 24) * 100,
          Math.random() * 100, // Placeholder for engagement
          ((channel.messageCount * channel.uniqueUsers) /
            (maxMessages * maxUsers)) *
            100,
        ],
        borderColor: [
          "rgba(25, 118, 210, 1)",
          "rgba(76, 175, 80, 1)",
          "rgba(255, 152, 0, 1)",
          "rgba(244, 67, 54, 1)",
          "rgba(156, 39, 176, 1)",
          "rgba(96, 125, 139, 1)",
        ][index],
        backgroundColor: [
          "rgba(25, 118, 210, 0.2)",
          "rgba(76, 175, 80, 0.2)",
          "rgba(255, 152, 0, 0.2)",
          "rgba(244, 67, 54, 0.2)",
          "rgba(156, 39, 176, 0.2)",
          "rgba(96, 125, 139, 0.2)",
        ][index],
        borderWidth: 2,
      };
    }),
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: "Channel Comparison (Normalized)",
      },
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      r: {
        beginAtZero: true,
        max: 100,
        ticks: {
          stepSize: 20,
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 2, height: 400 }}>
      <Radar data={chartData} options={options} />
    </Paper>
  );
}
