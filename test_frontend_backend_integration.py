#!/usr/bin/env python3
"""
Comprehensive test script to analyze backend capabilities and frontend integration issues.
This script will help identify what's working and what needs fixing.
"""

import requests
import json
import time
import argparse
from datetime import datetime, timedelta
from urllib.parse import urlencode
import sys

class FrontendBackendTester:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, details="", response_time=None, data=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        time_info = f" ({response_time:.2f}s)" if response_time else ""
        print(f"{status} {test_name}{time_info}")
        if details:
            print(f"    {details}")
        if data and isinstance(data, dict):
            print(f"    Data keys: {list(data.keys())}")
    
    def test_backend_endpoints(self):
        """Test all backend endpoints to understand capabilities"""
        print("\n🔧 Testing Backend Endpoints")
        print("-" * 50)
        
        endpoints = [
            ("/channels", "List channels"),
            ("/list", "List available logs"),
            ("/search?q=hello&jsonBasic=1", "Global search"),
            ("/advanced-search?q=hello&jsonBasic=1", "Advanced global search"),
            ("/analytics", "Global analytics"),
            ("/analytics/peak-activity", "Peak activity analysis"),
            ("/analytics/engagement-metrics", "Engagement metrics"),
        ]
        
        for endpoint, description in endpoints:
            try:
                start_time = time.time()
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=30)
                response_time = time.time() - start_time
                
                success = response.status_code == 200
                
                if success:
                    try:
                        data = response.json()
                        if isinstance(data, dict):
                            details = f"Status: {response.status_code}, Keys: {list(data.keys())}"
                        elif isinstance(data, list):
                            details = f"Status: {response.status_code}, Items: {len(data)}"
                        else:
                            details = f"Status: {response.status_code}, Type: {type(data)}"
                        self.log_test(description, True, details, response_time, data)
                    except:
                        details = f"Status: {response.status_code}, Length: {len(response.text)}"
                        self.log_test(description, True, details, response_time)
                else:
                    details = f"Status: {response.status_code}, Error: {response.text[:100]}"
                    self.log_test(description, False, details, response_time)
                    
            except Exception as e:
                self.log_test(description, False, f"Exception: {str(e)}")
    
    def test_global_search_functionality(self):
        """Test global search functionality specifically"""
        print("\n🔍 Testing Global Search Functionality")
        print("-" * 50)
        
        # Test simple global search
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/search?q=hello&jsonBasic=1")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                message_count = len(data.get('messages', []))
                self.log_test(
                    "Global Search - Simple Query",
                    True,
                    f"Found {message_count} messages",
                    response_time,
                    data
                )
            else:
                self.log_test(
                    "Global Search - Simple Query",
                    False,
                    f"Status: {response.status_code}",
                    response_time
                )
        except Exception as e:
            self.log_test("Global Search - Simple Query", False, f"Exception: {str(e)}")
        
        # Test advanced global search
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/advanced-search?q=hello&jsonBasic=1")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                message_count = len(data.get('messages', []))
                self.log_test(
                    "Global Search - Advanced Query",
                    True,
                    f"Found {message_count} messages",
                    response_time,
                    data
                )
            else:
                self.log_test(
                    "Global Search - Advanced Query",
                    False,
                    f"Status: {response.status_code}",
                    response_time
                )
        except Exception as e:
            self.log_test("Global Search - Advanced Query", False, f"Exception: {str(e)}")
    
    def test_analytics_endpoints(self):
        """Test analytics endpoints for data structure"""
        print("\n📊 Testing Analytics Endpoints")
        print("-" * 50)
        
        # Test global analytics
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/analytics")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                expected_fields = [
                    'totalChannels', 'totalMessages', 'totalUsers',
                    'topChannels', 'topUsers', 'messageDistribution'
                ]
                
                present_fields = [field for field in expected_fields if field in data]
                missing_fields = [field for field in expected_fields if field not in data]
                
                self.log_test(
                    "Analytics - Global Data Structure",
                    len(missing_fields) == 0,
                    f"Present: {present_fields}, Missing: {missing_fields}",
                    response_time,
                    data
                )
            else:
                self.log_test(
                    "Analytics - Global Data Structure",
                    False,
                    f"Status: {response.status_code}",
                    response_time
                )
        except Exception as e:
            self.log_test("Analytics - Global Data Structure", False, f"Exception: {str(e)}")
    
    def test_user_tracker_backend_support(self):
        """Test backend support for user tracker functionality"""
        print("\n👥 Testing User Tracker Backend Support")
        print("-" * 50)
        
        # Test advanced search with user filtering (used by user tracker)
        try:
            start_time = time.time()
            response = self.session.get(
                f"{self.base_url}/advanced-search?q=&users=testuser&jsonBasic=1&limit=20"
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                message_count = len(data.get('messages', []))
                self.log_test(
                    "User Tracker - User Filtering",
                    True,
                    f"Found {message_count} messages for user filter",
                    response_time,
                    data
                )
            else:
                self.log_test(
                    "User Tracker - User Filtering",
                    False,
                    f"Status: {response.status_code}",
                    response_time
                )
        except Exception as e:
            self.log_test("User Tracker - User Filtering", False, f"Exception: {str(e)}")
        
        # Test analytics with user filtering
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/analytics?users=testuser")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "User Tracker - Analytics Support",
                    True,
                    f"Analytics with user filter works",
                    response_time,
                    data
                )
            else:
                self.log_test(
                    "User Tracker - Analytics Support",
                    False,
                    f"Status: {response.status_code}",
                    response_time
                )
        except Exception as e:
            self.log_test("User Tracker - Analytics Support", False, f"Exception: {str(e)}")
    
    def analyze_message_rendering_data(self):
        """Analyze message data structure for rendering improvements"""
        print("\n💬 Analyzing Message Data Structure")
        print("-" * 50)
        
        try:
            # Get sample messages to analyze structure
            response = self.session.get(f"{self.base_url}/search?q=hello&jsonBasic=1&limit=5")
            
            if response.status_code == 200:
                data = response.json()
                messages = data.get('messages', [])
                
                if messages:
                    sample_message = messages[0]
                    message_fields = list(sample_message.keys()) if isinstance(sample_message, dict) else []
                    
                    self.log_test(
                        "Message Structure Analysis",
                        True,
                        f"Message fields: {message_fields}",
                        data=sample_message
                    )
                    
                    # Check for fields needed for good chat rendering
                    required_fields = ['text', 'timestamp', 'username', 'channel']
                    missing_required = [field for field in required_fields if field not in message_fields]
                    
                    self.log_test(
                        "Message Rendering Compatibility",
                        len(missing_required) == 0,
                        f"Missing required fields: {missing_required}" if missing_required else "All required fields present"
                    )
                else:
                    self.log_test(
                        "Message Structure Analysis",
                        False,
                        "No messages found in response"
                    )
            else:
                self.log_test(
                    "Message Structure Analysis",
                    False,
                    f"Failed to get messages: {response.status_code}"
                )
        except Exception as e:
            self.log_test("Message Structure Analysis", False, f"Exception: {str(e)}")
    
    def print_summary(self):
        """Print comprehensive test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['details']}")
        
        # Performance summary
        response_times = [r['response_time'] for r in self.test_results if r['response_time']]
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            print(f"\n⚡ Performance Summary:")
            print(f"  Average Response Time: {avg_time:.2f}s")
            print(f"  Fastest Response: {min_time:.2f}s")
            print(f"  Slowest Response: {max_time:.2f}s")
        
        # Save detailed results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        with open(f"frontend_backend_test_results_{timestamp}.json", "w") as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to frontend_backend_test_results_{timestamp}.json")
    
    def run_all_tests(self):
        """Run all test suites"""
        print(f"🧪 Starting Comprehensive Frontend-Backend Integration Tests")
        print(f"📡 Base URL: {self.base_url}")
        print("=" * 60)
        
        # Run test suites
        self.test_backend_endpoints()
        self.test_global_search_functionality()
        self.test_analytics_endpoints()
        self.test_user_tracker_backend_support()
        self.analyze_message_rendering_data()
        
        # Print summary
        self.print_summary()

def main():
    parser = argparse.ArgumentParser(description='Test frontend-backend integration')
    parser.add_argument('--url', default='http://localhost:3000', 
                       help='Base URL of the API (default: http://localhost:3000)')
    
    args = parser.parse_args()
    
    tester = FrontendBackendTester(args.url)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
