import React, { useState, useContext } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  IconButton,
  Autocomplete,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Fade,
  Zoom,
  Badge,
} from "@mui/material";
import {
  Person,
  Search,
  Message,
  Schedule,
  TrendingUp,
  Visibility,
  Add,
  Remove,
  FilterList,
} from "@mui/icons-material";
import { useQuery } from "react-query";
import { store } from "../store";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import styled from "styled-components";

dayjs.extend(relativeTime);

const TrackerContainer = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  .tracker-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
  }

  .user-search {
    margin-bottom: 16px;
  }

  .tracked-users {
    margin-bottom: 24px;
  }
`;

interface TrackedUser {
  userId: string;
  username: string;
  addedAt: Date;
  lastActivity?: Date;
  messageCount?: number;
  channelsActive?: number;
}

export function UserTracker() {
  const { state } = useContext(store);
  const [trackedUsers, setTrackedUsers] = useState<TrackedUser[]>([]);
  const [searchUsername, setSearchUsername] = useState("");
  const [selectedTimeRange, setSelectedTimeRange] = useState("7d");
  const [selectedUser, setSelectedUser] = useState<TrackedUser | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch user activity data for tracked users
  const {
    data: userActivities,
    isLoading: activitiesLoading,
    error: activitiesError,
  } = useQuery(
    [
      "tracked-user-activities",
      trackedUsers.map((u) => u.userId),
      selectedTimeRange,
    ],
    async () => {
      if (trackedUsers.length === 0) return [];

      const activities = await Promise.all(
        trackedUsers.map(async (user) => {
          try {
            // Get analytics data for proper statistics
            const analyticsParams = new URLSearchParams({
              users: user.username,
              from: dayjs()
                .subtract(parseInt(selectedTimeRange), "day")
                .toISOString(),
              to: dayjs().toISOString(),
              limit: "20",
            });

            const analyticsResponse = await fetch(
              `${state.apiBaseUrl}/analytics?${analyticsParams}`
            );

            let totalMessages = 0;
            let totalChannels = 0;
            let recentMessages = [];

            if (analyticsResponse.ok) {
              const analyticsData = await analyticsResponse.json();
              const userStats = analyticsData.topUsers?.find(
                (u: any) => u.username === user.username
              );
              if (userStats) {
                totalMessages = userStats.messageCount || 0;
                totalChannels = userStats.channelsActive || 0;
              }
            }

            // Get recent messages for display (limited sample)
            const messagesParams = new URLSearchParams({
              q: "",
              users: user.username,
              from: dayjs()
                .subtract(parseInt(selectedTimeRange), "day")
                .toISOString(),
              to: dayjs().toISOString(),
              limit: "20",
              jsonBasic: "1",
            });

            const messagesResponse = await fetch(
              `${state.apiBaseUrl}/advanced-search?${messagesParams}`
            );

            if (messagesResponse.ok) {
              const messagesData = await messagesResponse.json();
              recentMessages = messagesData.messages || [];
            }

            return {
              user,
              totalMessages,
              totalChannels,
              recentMessages,
              channels: [
                ...new Set(recentMessages?.map((m: any) => m.channel) || []),
              ],
            };
          } catch (error) {
            console.error(
              `Failed to fetch activity for ${user.username}:`,
              error
            );
            return {
              user,
              totalMessages: 0,
              totalChannels: 0,
              recentMessages: [],
              channels: [],
            };
          }
        })
      );

      return activities;
    },
    {
      enabled: trackedUsers.length > 0 && !!state.apiBaseUrl,
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  const addUserToTracker = () => {
    try {
      if (!searchUsername.trim()) return;

      const newUser: TrackedUser = {
        userId: searchUsername, // In a real app, you'd resolve this to actual user ID
        username: searchUsername.trim(),
        addedAt: new Date(),
      };

      if (!trackedUsers.find((u) => u.username === newUser.username)) {
        setTrackedUsers((prev) => [...prev, newUser]);
        setError(null);
      }
      setSearchUsername("");
    } catch (err) {
      setError(`Failed to add user: ${err}`);
      console.error("Error adding user to tracker:", err);
    }
  };

  const removeUserFromTracker = (username: string) => {
    setTrackedUsers((prev) => prev.filter((u) => u.username !== username));
  };

  const getUserActivity = (username: string) => {
    return userActivities?.find(
      (activity) => activity.user.username === username
    );
  };

  const formatTimeAgo = (date: Date) => {
    return dayjs(date).fromNow();
  };

  return (
    <TrackerContainer>
      <div className="tracker-header">
        <Visibility color="primary" fontSize="large" />
        <Typography variant="h4" component="h1">
          User Tracker
        </Typography>
      </div>

      <Fade in timeout={800}>
        <Paper
          sx={{
            p: 2,
            mb: 3,
            background: "linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)",
          }}
        >
          <Typography variant="body1" gutterBottom color="white">
            Track specific users across all channels to monitor their activity
            patterns, message frequency, and cross-channel presence.
          </Typography>
        </Paper>
      </Fade>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* User Search and Add */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Add User to Track
        </Typography>
        <Box display="flex" gap={2} alignItems="center">
          <TextField
            className="user-search"
            label="Username"
            value={searchUsername}
            onChange={(e) => setSearchUsername(e.target.value)}
            placeholder="Enter username to track..."
            size="small"
            onKeyDown={(e) => e.key === "Enter" && addUserToTracker()}
          />
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={addUserToTracker}
            disabled={!searchUsername.trim()}
          >
            Track User
          </Button>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={selectedTimeRange}
              label="Time Range"
              onChange={(e) => setSelectedTimeRange(e.target.value)}
            >
              <MenuItem value="1d">Last Day</MenuItem>
              <MenuItem value="7d">Last Week</MenuItem>
              <MenuItem value="30d">Last Month</MenuItem>
              <MenuItem value="90d">Last 3 Months</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Paper>

      {/* Tracked Users List */}
      {trackedUsers.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Tracked Users ({trackedUsers.length})
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1}>
            {trackedUsers.map((user) => {
              const activity = getUserActivity(user.username);
              const messageCount = activity?.totalMessages || 0;
              const channelCount = activity?.totalChannels || 0;

              return (
                <Zoom in key={user.username} timeout={300}>
                  <Badge
                    badgeContent={messageCount > 0 ? messageCount : null}
                    color="primary"
                    max={999}
                  >
                    <Chip
                      avatar={
                        <Avatar>
                          <Person />
                        </Avatar>
                      }
                      label={`${user.username} (${channelCount} channels)`}
                      onDelete={() => removeUserFromTracker(user.username)}
                      onClick={() => setSelectedUser(user)}
                      color={
                        selectedUser?.username === user.username
                          ? "primary"
                          : "default"
                      }
                      variant={
                        selectedUser?.username === user.username
                          ? "filled"
                          : "outlined"
                      }
                      sx={{
                        m: 0.5,
                        transition: "all 0.3s ease",
                        "&:hover": {
                          transform: "scale(1.05)",
                          boxShadow: 2,
                        },
                      }}
                    />
                  </Badge>
                </Zoom>
              );
            })}
          </Box>
        </Paper>
      )}

      {/* Activity Overview */}
      {activitiesLoading ? (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>Loading user activities...</Typography>
        </Box>
      ) : activitiesError ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load user activities: {(activitiesError as Error).message}
        </Alert>
      ) : userActivities && userActivities.length > 0 ? (
        <Grid container spacing={3}>
          {/* Activity Summary Cards */}
          <Grid item xs={12}>
            <Grid container spacing={2}>
              {userActivities.map((activity) => (
                <Grid item xs={12} sm={6} md={4} key={activity.user.username}>
                  <Card
                    sx={{
                      cursor: "pointer",
                      border:
                        selectedUser?.username === activity.user.username
                          ? 2
                          : 0,
                      borderColor: "primary.main",
                    }}
                    onClick={() => setSelectedUser(activity.user)}
                  >
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Avatar sx={{ width: 32, height: 32 }}>
                          <Person />
                        </Avatar>
                        <Typography variant="h6">
                          {activity.user.username}
                        </Typography>
                      </Box>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        gutterBottom
                      >
                        Added {formatTimeAgo(activity.user.addedAt)}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" mt={2}>
                        <Box textAlign="center">
                          <Typography variant="h6" color="primary">
                            {activity.totalMessages}
                          </Typography>
                          <Typography variant="caption">Messages</Typography>
                        </Box>
                        <Box textAlign="center">
                          <Typography variant="h6" color="secondary">
                            {activity.totalChannels}
                          </Typography>
                          <Typography variant="caption">Channels</Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* Detailed Activity for Selected User */}
          {selectedUser && (
            <Grid item xs={12}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Activity Details: {selectedUser.username}
                </Typography>
                {(() => {
                  const activity = getUserActivity(selectedUser.username);
                  if (!activity || activity.recentMessages.length === 0) {
                    return (
                      <Typography variant="body2" color="text.secondary">
                        No recent activity found for this user.
                      </Typography>
                    );
                  }

                  return (
                    <List sx={{ maxHeight: 400, overflow: "auto" }}>
                      {activity.recentMessages
                        .slice(0, 20)
                        .map((message: any, index: number) => (
                          <React.Fragment key={index}>
                            <ListItem alignItems="flex-start">
                              <ListItemAvatar>
                                <Avatar sx={{ bgcolor: "primary.main" }}>
                                  <Message />
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary={
                                  <Box
                                    display="flex"
                                    alignItems="center"
                                    gap={1}
                                    mb={0.5}
                                  >
                                    <Chip
                                      label={message.channel || "General"}
                                      size="small"
                                      color={
                                        message.channel
                                          ? "secondary"
                                          : "default"
                                      }
                                      variant="outlined"
                                    />
                                    <Typography
                                      variant="caption"
                                      color="text.secondary"
                                    >
                                      {dayjs(message.timestamp).format(
                                        "MMM D, HH:mm"
                                      )}
                                    </Typography>
                                  </Box>
                                }
                                secondary={
                                  <Typography
                                    variant="body2"
                                    color="text.primary"
                                    sx={{
                                      wordBreak: "break-word",
                                      mt: 0.5,
                                    }}
                                  >
                                    {message.text}
                                  </Typography>
                                }
                              />
                            </ListItem>
                            {index <
                              activity.recentMessages.slice(0, 20).length -
                                1 && <Divider variant="inset" component="li" />}
                          </React.Fragment>
                        ))}
                    </List>
                  );
                })()}
              </Paper>
            </Grid>
          )}
        </Grid>
      ) : trackedUsers.length > 0 ? (
        <Paper sx={{ p: 4, textAlign: "center" }}>
          <Typography variant="body1" color="text.secondary">
            No activity data available for tracked users.
          </Typography>
        </Paper>
      ) : (
        <Paper sx={{ p: 4, textAlign: "center" }}>
          <Typography variant="h6" gutterBottom>
            No Users Tracked
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Add users to track their activity across all channels.
          </Typography>
        </Paper>
      )}
    </TrackerContainer>
  );
}
