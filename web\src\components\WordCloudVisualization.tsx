import React from "react";
import ReactWordcloud from "react-wordcloud";
import { Box, Typography, CircularProgress, Alert } from "@mui/material";

interface WordCloudProps {
  data: Array<{
    text: string;
    value: number;
  }>;
  isLoading?: boolean;
  error?: string;
  title?: string;
}

export function WordCloudVisualization({ data, isLoading, error, title }: WordCloudProps) {
  const options = {
    colors: ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b"],
    enableTooltip: true,
    deterministic: false,
    fontFamily: "impact",
    fontSizes: [5, 60],
    fontStyle: "normal",
    fontWeight: "normal",
    padding: 1,
    rotations: 3,
    rotationAngles: [0, 90],
    scale: "sqrt",
    spiral: "archimedean",
    transitionDuration: 1000,
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <Typography variant="body2" color="text.secondary">
          No word data available
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {title && (
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
      )}
      <Box sx={{ height: 400, width: "100%" }}>
        <ReactWordcloud words={data} options={options} />
      </Box>
    </Box>
  );
}
